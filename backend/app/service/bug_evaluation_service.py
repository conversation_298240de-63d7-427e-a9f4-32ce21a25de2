"""
BUG评估数据服务层
负责将BUG数据和评估结果保存到数据库
"""
from typing import Dict, List, Any, Optional
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from backend.app.service.title_score import TitleFieldEvaluation
from backend.app.service.description_socre import DimensionFieldEvaluation
from backend.app.service.suggest.field_evaluation_models import SuggestFieldEvaluation
from backend.app.database.database import get_db
from backend.app.utils.logger_util import logger
from datetime import datetime, timedelta
from backend.app.models.bug import BugEvaluation, TitleEvaluation, DescriptionEvaluation, SuggestEvaluation
from backend.app.utils.time_util import get_china_now
from backend.app.service.cards import send_daily_stats_card
from backend.app.config.config import ENABLE_DAILY_STATS_PUSH, JIANKANG_WORKSPACE_ID, YIBAO_WORKSPACE_ID
from backend.app.utils.tapd import tap_client

async def save_bug_evaluation(
    bug_data: Dict[str, Any],
    workspace_id: str,
    overall_passed: bool,
    common_missing: List[str],
    dimension_evaluations: List[DimensionFieldEvaluation],
    title_evaluations: List[TitleFieldEvaluation],
    suggest_evaluations: List[SuggestFieldEvaluation]
) -> Optional[int]:
    """
    保存BUG评估结果到数据库
    如果相同bug_id和workspace_id的记录已存在，则覆盖现有记录

    Args:
        bug_data: 完整的BUG数据
        workspace_id: 工作空间ID
        overall_passed: 整体是否通过
        common_missing: 缺失的普通字段
        dimension_evaluations: 详细描述评估结果
        title_evaluations: 标题评估结果
        suggest_evaluations: 智能建议评估结果

    Returns:
        保存成功返回评估记录ID，失败返回None
    """
    try:
        # 延迟导入模型以避免循环导入
        import sys
        import os
        # 确保项目根目录在Python路径中
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../'))
        sys.path.insert(0, project_root)

        # 获取数据库会话
        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            bug_id = bug_data.get("ID", "")
            is_update = False  # 标记是否为更新操作

            # 查找是否已存在相同bug_id和workspace_id的记录
            existing_evaluation = db.query(BugEvaluation).filter(
                and_(
                    BugEvaluation.bug_id == bug_id,
                    BugEvaluation.workspace_id == workspace_id
                )
            ).first()

            if existing_evaluation:
                # 更新现有记录
                is_update = True
                logger.info(f"发现已存在的BUG评估记录，将覆盖更新，BUG ID: {bug_id}, 评估记录ID: {existing_evaluation.id}")

                # 更新主记录字段
                existing_evaluation.title = bug_data.get("标题", "")
                existing_evaluation.creator = bug_data.get("创建人", "")
                existing_evaluation.status = bug_data.get("状态", "")
                existing_evaluation.priority = bug_data.get("优先级", "")
                existing_evaluation.severity = bug_data.get("严重程度", "")
                existing_evaluation.description = bug_data.get("详细描述", "")
                existing_evaluation.module = bug_data.get("模块", "")
                existing_evaluation.bug_data = bug_data
                existing_evaluation.overall_passed = overall_passed
                existing_evaluation.common_missing_fields = common_missing
                existing_evaluation.evaluated_at = get_china_now()
                existing_evaluation.updated_at = get_china_now()
                logger.info(existing_evaluation.evaluated_at)
                # 删除现有的关联评估记录（由于设置了cascade="all, delete-orphan"，会自动删除）
                # 清空关联记录列表，SQLAlchemy会自动删除这些记录
                existing_evaluation.title_evaluations.clear()
                existing_evaluation.description_evaluations.clear()
                existing_evaluation.suggest_evaluations.clear()

                bug_evaluation = existing_evaluation
            else:
                # 创建新的主评估记录
                logger.info(f"创建新的BUG评估记录，BUG ID: {bug_id}")
                bug_evaluation = BugEvaluation(
                    bug_id=bug_id,
                    workspace_id=workspace_id,
                    title=bug_data.get("标题", ""),
                    creator=bug_data.get("创建人", ""),
                    status=bug_data.get("状态", ""),
                    priority=bug_data.get("优先级", ""),
                    severity=bug_data.get("严重程度", ""),
                    description=bug_data.get("详细描述", ""),
                    module=bug_data.get("模块", ""),
                    bug_data=bug_data,  # 完整的BUG数据
                    overall_passed=overall_passed,
                    common_missing_fields=common_missing,
                    evaluated_at=get_china_now(),
                    is_intercepted=overall_passed is False
                )

                db.add(bug_evaluation)

            db.flush()  # 获取主记录ID
            
            # 保存标题评估结果
            for title_eval in title_evaluations:
                title_evaluation = TitleEvaluation(
                    bug_evaluation_id=bug_evaluation.id,
                    field=title_eval.field,
                    dimension_scores=title_eval.dimension_scores,
                    passed=title_eval.passed,
                    feedback=title_eval.feedback,
                    suggest=title_eval.suggest,
                    thinking_stage1=title_eval.thinking_stage1,
                    thinking_stage2=title_eval.thinking_stage2,
                    needed_elements=title_eval.needed_elements,
                    element_reason=title_eval.element_reason
                )
                db.add(title_evaluation)
            
            # 保存详细描述评估结果
            for desc_eval in dimension_evaluations:
                description_evaluation = DescriptionEvaluation(
                    bug_evaluation_id=bug_evaluation.id,
                    field=desc_eval.field,
                    dimension_scores=desc_eval.dimension_scores,
                    score=desc_eval.score,
                    passed=desc_eval.passed,
                    feedback=desc_eval.feedback,
                    suggest=desc_eval.suggest
                )
                db.add(description_evaluation)
            
            # 保存智能建议评估结果
            for suggest_eval in suggest_evaluations:
                suggest_evaluation = SuggestEvaluation(
                    bug_evaluation_id=bug_evaluation.id,
                    field=suggest_eval.field,
                    suggested=suggest_eval.suggested,
                    actual=suggest_eval.actual,
                    reason=suggest_eval.reason,
                    feedback=suggest_eval.feedback
                )
                db.add(suggest_evaluation)
            
            # 提交事务
            db.commit()

            operation_type = "更新" if is_update else "创建"
            logger.info(f"成功{operation_type}BUG评估结果，BUG ID: {bug_data.get('ID', '')}, 评估记录ID: {bug_evaluation.id}")
            return bug_evaluation.id
            
        except Exception as e:
            db.rollback()
            logger.error(f"保存BUG评估结果时发生数据库错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return None
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"获取数据库连接失败: {str(e)}")
        return None


async def get_bug_evaluation_by_id(evaluation_id: int) -> Optional[Dict[str, Any]]:
    """
    根据评估记录ID获取完整的评估结果
    
    Args:
        evaluation_id: 评估记录ID
    
    Returns:
        评估结果字典，失败返回None
    """
    try:
        # 延迟导入模型
        import sys
        import os
        # 确保项目根目录在Python路径中
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../'))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)

        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            bug_evaluation = db.query(BugEvaluation).filter(
                BugEvaluation.id == evaluation_id
            ).first()
            
            if not bug_evaluation:
                return None
            
            # 构建返回结果
            result = {
                "id": bug_evaluation.id,
                "bug_id": bug_evaluation.bug_id,
                "workspace_id": bug_evaluation.workspace_id,
                "title": bug_evaluation.title,
                "creator": bug_evaluation.creator,
                "overall_passed": bug_evaluation.overall_passed,
                "common_missing_fields": bug_evaluation.common_missing_fields,
                "bug_data": bug_evaluation.bug_data,
                "evaluated_at": bug_evaluation.evaluated_at.isoformat() if bug_evaluation.evaluated_at else None,
                "title_evaluations": [
                    {
                        "field": te.field,
                        "dimension_scores": te.dimension_scores,
                        "passed": te.passed,
                        "feedback": te.feedback,
                        "suggest": te.suggest,
                        "needed_elements": te.needed_elements,
                        "element_reason": te.element_reason
                    }
                    for te in bug_evaluation.title_evaluations
                ],
                "description_evaluations": [
                    {
                        "field": de.field,
                        "dimension_scores": de.dimension_scores,
                        "score": de.score,
                        "passed": de.passed,
                        "feedback": de.feedback,
                        "suggest": de.suggest
                    }
                    for de in bug_evaluation.description_evaluations
                ],
                "suggest_evaluations": [
                    {
                        "field": se.field,
                        "suggested": se.suggested,
                        "actual": se.actual,
                        "reason": se.reason,
                        "feedback": se.feedback
                    }
                    for se in bug_evaluation.suggest_evaluations
                ]
            }
            
            return result
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"获取BUG评估结果失败: {str(e)}")
        return None


async def get_bug_evaluations_by_bug_id(bug_id: str) -> List[Dict[str, Any]]:
    """
    根据BUG ID获取所有评估记录
    
    Args:
        bug_id: BUG ID
    
    Returns:
        评估记录列表
    """
    try:
        # 延迟导入模型
        import sys
        import os
        # 确保项目根目录在Python路径中
        project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '../../../'))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)


        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            evaluations = db.query(BugEvaluation).filter(
                BugEvaluation.bug_id == bug_id
            ).order_by(BugEvaluation.evaluated_at.desc()).all()
            
            results = []
            for evaluation in evaluations:
                result = {
                    "id": evaluation.id,
                    "overall_passed": evaluation.overall_passed,
                    "evaluated_at": evaluation.evaluated_at.isoformat() if evaluation.evaluated_at else None,
                    "title_evaluations_count": len(evaluation.title_evaluations),
                    "description_evaluations_count": len(evaluation.description_evaluations),
                    "suggest_evaluations_count": len(evaluation.suggest_evaluations)
                }
                results.append(result)
            
            return results
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"获取BUG评估历史失败: {str(e)}")
        return []


async def get_daily_bug_interception_stats(target_date: str = None) -> Dict[str, Any]:
    """
    获取指定日期的BUG拦截统计数据，并发送卡片，按workspace（业务）区分健康与医保业务
    """
    try:
        if target_date is None:
            target_date = datetime.now().strftime("%Y-%m-%d")

        try:
            date_obj = datetime.strptime(target_date, "%Y-%m-%d")
        except ValueError:
            logger.error(f"日期格式错误: {target_date}")
            return {"error": "日期格式错误，请使用YYYY-MM-DD格式"}

        start_date = date_obj.replace(hour=0, minute=0, second=0, microsecond=0)
        end_date = start_date + timedelta(days=1)

        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            evaluations = db.query(BugEvaluation).filter(
                and_(
                    BugEvaluation.created_at >= start_date,
                    BugEvaluation.created_at < end_date,
                    BugEvaluation.is_intercepted == True
                )
            ).all()

            stats_by_ws = {
                "all": [],
                "health": [],
                "medical": []
            }
            for evaluation in evaluations:
                stats_by_ws["all"].append(evaluation)
                if evaluation.workspace_id == JIANKANG_WORKSPACE_ID:
                    stats_by_ws["health"].append(evaluation)
                elif evaluation.workspace_id == YIBAO_WORKSPACE_ID:
                    stats_by_ws["medical"].append(evaluation)

            def compute_stats(evals: list) -> Dict[str, Any]:
                total = len(evals)
                desc_fp = sum(1 for e in evals if e.desc_fp)
                desc_valid = sum(1 for e in evals if e.desc_invalid is False)
                title_fp = sum(1 for e in evals if e.title_fp)
                title_valid = sum(1 for e in evals if e.title_invalid is False)

                # 修复率：非误报（desc_fp=False 且 title_fp=False）
                non_fp_evals = [e for e in evals if not e.desc_fp and not e.title_fp]
                non_fp_count = len(non_fp_evals)
                fixed_count = sum(1 for e in non_fp_evals if e.overall_passed)

                return {
                    "总拦截BUG数量": total,
                    "详细描述误报数量": desc_fp,
                    "详细描述误报百分比": round((desc_fp / total * 100) if total else 0, 2),
                    "详细描述建议有效数量": desc_valid,
                    "详细描述建议有效百分比": round((desc_valid / total * 100) if total else 0, 2),
                    "标题误报数量": title_fp,
                    "标题误报百分比": round((title_fp / total * 100) if total else 0, 2),
                    "标题建议有效数量": title_valid,
                    "标题建议有效百分比": round((title_valid / total * 100) if total else 0, 2),
                    "BUG修复率": round((fixed_count / non_fp_count * 100) if non_fp_count else 0, 2),
                    "修复BUG数量": fixed_count,
                    "可修复BUG数量": non_fp_count
                }

            result = {
                "统计日期": target_date,
                "总业务统计": compute_stats(stats_by_ws["all"]),
                "健康业务统计": compute_stats(stats_by_ws["health"]),
                "医保业务统计": compute_stats(stats_by_ws["medical"]),
            }

            if ENABLE_DAILY_STATS_PUSH:
                try:
                    await send_daily_stats_card(result)
                    logger.info(f"成功推送{target_date}的BUG拦截统计卡片")
                except Exception as e:
                    logger.error(f"推送{target_date}的BUG拦截统计卡片失败: {str(e)}")
                    return {"error": f"推送失败: {str(e)}"}

            logger.info(f"成功处理{target_date}的BUG拦截统计")
            return {"msg": f"{target_date} 的BUG拦截统计卡片已发送"}

        finally:
            db.close()

    except Exception as e:
        logger.error(f"获取BUG拦截统计数据失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return {"error": f"获取统计数据失败: {str(e)}"}

def check_bug_evaluation_exists(bug_id: str, workspace_id: str, bug_title: str, bug_description_fix: str) -> tuple[
    bool, bool, bool, bool]:
    """
    检查BUG评估记录是否已存在以及是否已通过

    Args:
        bug_description_fix:
        bug_title:
        bug_id: BUG ID
        workspace_id: 工作空间ID

    Returns:
        tuple: (是否存在评估记录, 是否已通过评估)
    """
    try:
        # 获取数据库会话
        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            # 查询是否已存在相同bug_id和workspace_id的记录
            existing_evaluation = db.query(BugEvaluation).filter(
                and_(
                    BugEvaluation.bug_id == bug_id,
                    BugEvaluation.workspace_id == workspace_id,
                )
            ).first()
            is_fp = False
            if existing_evaluation:
                # 记录存在，检查是否已通过评估
                is_passed = existing_evaluation.overall_passed
                logger.debug(f"BUG {bug_id} 已存在评估记录，通过状态: {is_passed}")
                fix_flag = False
                if existing_evaluation.title != bug_title or existing_evaluation.description != bug_description_fix:
                    fix_flag = True
                if existing_evaluation.title_fp or existing_evaluation.desc_fp:
                    is_fp = True
                return True, is_passed, fix_flag, is_fp
            else:
                # 记录不存在
                logger.debug(f"BUG {bug_id} 未找到评估记录")
                return False, False, False, False

        finally:
            db.close()

    except Exception as e:
        logger.error(f"检查BUG评估记录失败 {bug_id}: {str(e)}")
        # 出现异常时，为了安全起见，返回不存在记录
        return False, False, False, False


async def update_bug_evaluation_feedback(bug_id: str, action_value: str, operator: str) -> bool:
    """
    根据action_value更新BUG评估记录的反馈字段

    Args:
        bug_id: BUG ID
        action_value: 操作值（desc_fp, desc_invalid, title_fp, title_invalid, title_replace）
        operator: 操作人员

    Returns:
        更新成功返回True，失败返回False
    """
    try:
        # 获取数据库会话
        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            # 查找最新的BUG评估记录
            bug_evaluation = db.query(BugEvaluation).filter(
                BugEvaluation.bug_id == bug_id
            ).order_by(BugEvaluation.evaluated_at.desc()).first()

            if not bug_evaluation:
                logger.warning(f"未找到BUG ID {bug_id} 的评估记录")
                return False

            # 根据action_value更新对应的字段
            current_time = get_china_now()

            if action_value == "desc_fp":
                # 详细描述误报
                bug_evaluation.desc_fp = True
                logger.info(f"标记BUG {bug_id} 详细描述为误报")

            elif action_value == "desc_invalid":
                # 详细描述建议无效
                bug_evaluation.desc_invalid = True
                logger.info(f"标记BUG {bug_id} 详细描述建议无效")

            elif action_value == "title_fp":
                # 标题误报
                bug_evaluation.title_fp = True
                logger.info(f"标记BUG {bug_id} 标题为误报")

            elif action_value == "title_invalid":
                # 标题建议无效
                bug_evaluation.title_invalid = True
                logger.info(f"标记BUG {bug_id} 标题建议无效")

            elif action_value == "title_replace":
                # 标题替换操作（仅记录操作，不修改特定字段）
                logger.info(f"记录BUG {bug_id} 标题替换操作")

            else:
                logger.warning(f"未知的action_value: {action_value}")
                return False

            # 更新操作信息
            bug_evaluation.feedback_operator = operator
            bug_evaluation.feedback_timestamp = current_time
            bug_evaluation.feedback_action_value = action_value
            bug_evaluation.updated_at = current_time

            # 提交更改
            db.commit()

            logger.info(f"成功更新BUG {bug_id} 的反馈信息，操作: {action_value}，操作人: {operator}")
            return True

        except Exception as e:
            db.rollback()
            logger.error(f"更新BUG评估反馈时发生数据库错误: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            db.close()

    except Exception as e:
        logger.error(f"获取数据库连接失败: {str(e)}")
        return False


async def get_title_suggestion_by_bug_id(bug_id: str) -> tuple[str, str]:
    """
    根据BUG ID获取最新的标题建议

    Args:
        bug_id: BUG ID

    Returns:
        标题建议字符串，如果未找到则返回None
    """
    try:
        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            # 直接通过JOIN查询获取标题建议
            # 查找最新的BUG评估记录及其对应的标题评估记录
            result = db.query(
                TitleEvaluation.suggest,
                BugEvaluation.workspace_id
            ).join(
                BugEvaluation, TitleEvaluation.bug_evaluation_id == BugEvaluation.id
            ).filter(
                BugEvaluation.bug_id == bug_id
            ).order_by(
                BugEvaluation.evaluated_at.desc()
            ).first()

            if not result:
                logger.warning(f"未找到BUG ID {bug_id} 的标题评估记录")
                return None

            suggest = result[0]  # result是一个tuple，取第一个元素
            workspace_id = result[1]  # 获取工作空间ID
            if not suggest or suggest.strip() == "":
                logger.warning(f"BUG ID {bug_id} 的标题建议为空")
                return None

            logger.info(f"成功获取BUG ID {bug_id} 的标题建议: {suggest} 工作空间ID: {workspace_id}")
            return suggest, workspace_id

        finally:
            db.close()

    except Exception as e:
        logger.error(f"获取标题建议失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


async def update_bug_evaluation_ignore_reason(workspace_id: str, bug_id: str, reason: str, operator: str) -> bool:
    """
    根据 workspace_id 和 bug_id 更新 BUG 评估记录中的 reason 字段

    Args:
        workspace_id: TAPD 工作空间 ID
        bug_id: TAPD 缺陷 ID
        reason: 忽略原因（用户填写的内容）
        operator: 操作人姓名

    Returns:
        成功返回 True，失败返回 False
    """
    try:
        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            bug_evaluation = db.query(BugEvaluation).filter(
                and_(
                    BugEvaluation.workspace_id == workspace_id,
                    BugEvaluation.bug_id == bug_id
                )
            ).order_by(BugEvaluation.evaluated_at.desc()).first()

            if not bug_evaluation:
                logger.warning(f"未找到 workspace_id={workspace_id}, bug_id={bug_id} 的 BUG 评估记录")
                return False

            bug_evaluation.reason = reason
            bug_evaluation.feedback_operator = operator
            bug_evaluation.feedback_timestamp = get_china_now()
            bug_evaluation.updated_at = get_china_now()

            db.commit()
            logger.info(f"已更新 BUG {bug_id} 忽略原因为: {reason}，操作人: {operator}")
            return True

        except Exception as e:
            db.rollback()
            logger.error(f"更新 BUG 忽略原因失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            db.close()

    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        return False


def update_bug_evaluation_is_closed_without_push(workspace_id: str, bug_id: str, is_closed: bool) -> bool:
    """
    根据 workspace_id 和 bug_id 更新 BUG 评估记录中的 is_closed_without_push 字段

    Args:
        workspace_id: TAPD 工作空间 ID
        bug_id: TAPD 缺陷 ID
        is_closed: 是否因缺陷状态关闭未推送

    Returns:
        成功返回 True，失败返回 False
    """
    try:
        db_gen = get_db()
        db: Session = next(db_gen)

        try:
            bug_evaluation = db.query(BugEvaluation).filter(
                and_(
                    BugEvaluation.workspace_id == workspace_id,
                    BugEvaluation.bug_id == bug_id
                )
            ).order_by(BugEvaluation.evaluated_at.desc()).first()

            if not bug_evaluation:
                logger.warning(f"未找到 workspace_id={workspace_id}, bug_id={bug_id} 的 BUG 评估记录")
                return False

            bug_evaluation.is_closed_without_push = is_closed
            bug_evaluation.updated_at = get_china_now()

            db.commit()
            logger.info(f"已更新 BUG {bug_id} 的 is_closed_without_push 字段为: {is_closed}")
            return True

        except Exception as e:
            db.rollback()
            logger.error(f"更新 is_closed_without_push 字段失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            db.close()

    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        return False


def get_bug_from_dataset(workspace_id: str) -> list:
    try:
        db_gen = get_db()
        db: Session = next(db_gen)
        bugs = db.query(BugEvaluation.bug_id).filter(BugEvaluation.workspace_id == workspace_id).all()
        db.close()
        bug_id_list = [bug.bug_id for bug in bugs]
        pure_bug_data_list = []
        for bug_id in bug_id_list:
            pure_bug_data = tap_client.get_bug_all_pure_message(workspace_id, bug_id)
            pure_bug_data_list.append(pure_bug_data)
        return pure_bug_data_list
    except Exception as e:
        logger.error(f"获取数据集失败: {str(e)}")
        return []
